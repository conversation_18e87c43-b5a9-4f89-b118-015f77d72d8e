package com.bikeana.web;

import com.bikeana.listing.api.BikeListingResponseDto;
import com.bikeana.listing.api.CreateBikeListingRequestDto;
import com.bikeana.listing.domain.BikeListingService;
import com.bikeana.listing.domain.ListingStatus;
import com.bikeana.security.JwtAuthenticationFilter;
import com.bikeana.security.JwtService;
import com.bikeana.user.api.SignupRequestDto;
import com.bikeana.user.api.UserProfileUpdateRequestDto;
import com.bikeana.user.api.UserResponseDto;
import com.bikeana.user.domain.User;
import com.bikeana.user.domain.UserService;
import io.quarkus.qute.Engine;
import io.quarkus.qute.TemplateInstance;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

/**
 * HTMX controller for dynamic web interactions.
 * Handles form submissions and partial page updates using HTMX.
 */
@Controller
@RequestMapping("/htmx")
public class HtmxController {

    private final UserService userService;
    private final BikeListingService bikeListingService;
    private final JwtService jwtService;
    private final Engine quteEngine;

    public HtmxController(UserService userService, BikeListingService bikeListingService, JwtService jwtService, Engine quteEngine) {
        this.userService = userService;
        this.bikeListingService = bikeListingService;
        this.jwtService = jwtService;
        this.quteEngine = quteEngine;
    }

    /**
     * Adds the current year to all views for dynamic copyright notices.
     */
    @ModelAttribute("currentYear")
    public int currentYear() {
        return LocalDate.now().getYear();
    }

    /**
     * DTO for login requests in HTMX forms.
     */
    public static class LoginRequest {
        private String email;
        private String password;

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
    }

    /**
     * Handle user signup via HTMX.
     */
    @PostMapping("/auth/signup")
    public String signup(@Valid @ModelAttribute SignupRequestDto signupRequest,
                         BindingResult bindingResult,
                         HttpServletResponse response,
                         Model model) {
        if (bindingResult.hasErrors()) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Please fix the validation errors and try again.");
            model.addAttribute("errors", bindingResult.getAllErrors());
            return "alert";
        }

        try {
            // Register the user
            UserResponseDto user = userService.registerUser(signupRequest);

            // Generate tokens
            String accessToken = jwtService.generateAccessToken(user.id(), user.email(), user.role());
            String refreshToken = jwtService.generateRefreshToken(user.id());

            // Set JWT token as HTTP-only cookie
            Cookie accessCookie = new Cookie("access_token", accessToken);
            accessCookie.setHttpOnly(true);
            accessCookie.setPath("/");
            accessCookie.setMaxAge(15 * 60); // 15 minutes
            response.addCookie(accessCookie);

            Cookie refreshCookie = new Cookie("refresh_token", refreshToken);
            refreshCookie.setHttpOnly(true);
            refreshCookie.setPath("/");
            refreshCookie.setMaxAge(7 * 24 * 60 * 60); // 7 days
            response.addCookie(refreshCookie);

            // Set HTMX redirect header
            response.setHeader("HX-Redirect", "/");

            model.addAttribute("type", "success");
            model.addAttribute("message", "Account created successfully! Redirecting to home page...");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";

        } catch (UserService.UserAlreadyExistsException e) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "An account with this email already exists.");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";
        } catch (Exception e) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Failed to create account. Please try again.");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";
        }
    }

    /**
     * Handle user login via HTMX.
     */
    @PostMapping("/auth/login")
    public String login(@Valid @ModelAttribute LoginRequest loginRequest,
                        BindingResult bindingResult,
                        HttpServletResponse response,
                        Model model) {
        if (bindingResult.hasErrors()) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Please enter valid email and password.");
            model.addAttribute("errors", bindingResult.getAllErrors());
            return "alert";
        }

        try {
            // Authenticate user
            Optional<User> userOpt = userService.authenticateUser(loginRequest.getEmail(), loginRequest.getPassword());

            if (userOpt.isEmpty()) {
                model.addAttribute("type", "error");
                model.addAttribute("message", "Invalid email or password.");
                model.addAttribute("errors", java.util.Collections.emptyList());
                return "alert";
            }

            User user = userOpt.get();

            // Check if user is active
            if (!"ACTIVE".equals(user.getStatus())) {
                model.addAttribute("type", "error");
                model.addAttribute("message", "Account is not active. Please contact support.");
                model.addAttribute("errors", java.util.Collections.emptyList());
                return "alert";
            }

            // Generate tokens
            String accessToken = jwtService.generateAccessToken(user.getId(), user.getEmail(), user.getRole());
            String refreshToken = jwtService.generateRefreshToken(user.getId());

            // Set JWT token as HTTP-only cookie
            Cookie accessCookie = new Cookie("access_token", accessToken);
            accessCookie.setHttpOnly(true);
            accessCookie.setPath("/");
            accessCookie.setMaxAge(15 * 60); // 15 minutes
            response.addCookie(accessCookie);

            Cookie refreshCookie = new Cookie("refresh_token", refreshToken);
            refreshCookie.setHttpOnly(true);
            refreshCookie.setPath("/");
            refreshCookie.setMaxAge(7 * 24 * 60 * 60); // 7 days
            response.addCookie(refreshCookie);

            // Set HTMX redirect header
            response.setHeader("HX-Redirect", "/");

            model.addAttribute("type", "success");
            model.addAttribute("message", "Login successful! Redirecting to home page...");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";

        } catch (Exception e) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Login failed. Please try again.");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";
        }
    }

    /**
     * Handle user profile update via HTMX.
     */
    @PostMapping("/users/profile")
    public String updateProfile(@Valid @ModelAttribute UserProfileUpdateRequestDto updateRequest,
                                BindingResult bindingResult,
                                Authentication authentication,
                                Model model) {
        if (bindingResult.hasErrors()) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Please fix the validation errors and try again.");
            model.addAttribute("errors", bindingResult.getAllErrors());
            return "alert";
        }

        try {
            JwtAuthenticationFilter.UserPrincipal principal =
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

            Optional<UserResponseDto> userOpt = userService.updateUserProfile(
                    principal.getUserId(),
                    updateRequest.fullName(),
                    updateRequest.phone()
            );

            if (userOpt.isEmpty()) {
                model.addAttribute("type", "error");
                model.addAttribute("message", "User not found.");
                return "alert";
            }

            model.addAttribute("user", userOpt.get());
            model.addAttribute("message", "Profile updated successfully!");
            return "userCard";

        } catch (Exception e) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Failed to update profile. Please try again.");
            return "alert";
        }
    }

    /**
     * Get user profile form via HTMX.
     */
    @GetMapping("/users/profile/edit")
    public String getProfileForm(Authentication authentication, Model model) {
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

        var user = userService.findUserById(principal.getUserId());

        model.addAttribute("user", user.orElse(null));
        return "profileForm";
    }

    /**
     * Handle logout via HTMX.
     */
    @PostMapping("/auth/logout")
    public String logout(HttpServletResponse response, Model model) {
        // Clear JWT cookies
        Cookie accessCookie = new Cookie("access_token", "");
        accessCookie.setHttpOnly(true);
        accessCookie.setPath("/");
        accessCookie.setMaxAge(0);
        response.addCookie(accessCookie);

        Cookie refreshCookie = new Cookie("refresh_token", "");
        refreshCookie.setHttpOnly(true);
        refreshCookie.setPath("/");
        refreshCookie.setMaxAge(0);
        response.addCookie(refreshCookie);

        // Set HTMX redirect header
        response.setHeader("HX-Redirect", "/");

        model.addAttribute("type", "success");
        model.addAttribute("message", "Logged out successfully!");
        return "alert";
    }

    /**
     * Handle bike listing creation via HTMX.
     */
    @PostMapping("/listings/create")
    public String createListing(@Valid @ModelAttribute CreateBikeListingRequestDto listingRequest,
                               BindingResult bindingResult,
                               Authentication authentication,
                               HttpServletResponse response,
                               Model model) {
        if (bindingResult.hasErrors()) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Please fix the validation errors and try again.");
            model.addAttribute("errors", bindingResult.getAllErrors());
            return "alert";
        }

        try {
            JwtAuthenticationFilter.UserPrincipal principal =
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

            BikeListingResponseDto listing = bikeListingService.createListing(listingRequest, principal.getUserId());

            // Set HTMX redirect header to the photo capture page
            response.setHeader("HX-Redirect", "/listings/" + listing.id() + "/capture-photos");

            model.addAttribute("type", "success");
            model.addAttribute("message", "Listing created successfully! Now let's add photos...");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";

        } catch (IllegalArgumentException e) {
            model.addAttribute("type", "error");
            model.addAttribute("message", e.getMessage());
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";
        } catch (Exception e) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Failed to create listing. Please try again.");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";
        }
    }

    /**
     * Handle listing status update via HTMX.
     */
    @PostMapping("/listings/{listingId}/status")
    public String updateListingStatus(@PathVariable UUID listingId,
                                     @RequestParam ListingStatus status,
                                     Authentication authentication,
                                     Model model) {
        try {
            JwtAuthenticationFilter.UserPrincipal principal =
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

            Optional<BikeListingResponseDto> listing = bikeListingService.updateListingStatus(
                    listingId, status, principal.getUserId());

            if (listing.isEmpty()) {
                model.addAttribute("type", "error");
                model.addAttribute("message", "Listing not found.");
                return "alert";
            }

            model.addAttribute("type", "success");
            model.addAttribute("message", "Listing status updated to " + status.getDisplayName());
            return "alert";

        } catch (IllegalArgumentException e) {
            model.addAttribute("type", "error");
            model.addAttribute("message", e.getMessage());
            return "alert";
        } catch (Exception e) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Failed to update listing status. Please try again.");
            return "alert";
        }
    }

    /**
     * Handle listing reservation via HTMX.
     */
    @PostMapping("/listings/{listingId}/reserve")
    public String reserveListing(@PathVariable UUID listingId,
                                @RequestParam(defaultValue = "24") int hours,
                                Authentication authentication,
                                Model model) {
        try {
            JwtAuthenticationFilter.UserPrincipal principal =
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

            Optional<BikeListingResponseDto> listing = bikeListingService.reserveListing(
                    listingId, principal.getUserId(), hours);

            if (listing.isEmpty()) {
                model.addAttribute("type", "error");
                model.addAttribute("message", "Listing not found.");
                return "alert";
            }

            model.addAttribute("type", "success");
            model.addAttribute("message", "Listing reserved successfully! You have " + hours + " hours to complete your purchase.");
            return "alert";

        } catch (IllegalArgumentException e) {
            model.addAttribute("type", "error");
            model.addAttribute("message", e.getMessage());
            return "alert";
        } catch (Exception e) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Failed to reserve listing. Please try again.");
            return "alert";
        }
    }
}
