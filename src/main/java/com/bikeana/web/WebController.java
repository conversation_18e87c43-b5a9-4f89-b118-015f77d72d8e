package com.bikeana.web;

import com.bikeana.listing.api.BikeListingResponseDto;
import com.bikeana.listing.api.BikeListingSummaryDto;
import com.bikeana.listing.domain.*;
import com.bikeana.security.JwtAuthenticationFilter;
import com.bikeana.user.domain.UserService;
import io.quarkus.qute.Engine;
import io.quarkus.qute.TemplateInstance;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Web controller for serving HTML pages using Qute templates.
 * Handles the main web interface for the Bikeana application.
 */
@Controller
public class WebController {

    private final UserService userService;
    private final BikeListingService bikeListingService;
    private final Engine quteEngine;

    public WebController(UserService userService, BikeListingService bikeListingService, Engine quteEngine) {
        this.userService = userService;
        this.bikeListingService = bikeListingService;
        this.quteEngine = quteEngine;
    }

    /**
     * Adds the current year to all views for dynamic copyright notices.
     */
    @ModelAttribute("currentYear")
    public int currentYear() {
        return LocalDate.now().getYear();
    }

    /**
     * Home page - shows landing page or redirects to dashboard if authenticated.
     */
    @GetMapping("/")
    public String index(Authentication authentication, Model model) {
        boolean isAuthenticated = authentication != null && authentication.isAuthenticated();
        model.addAttribute("isAuthenticated", isAuthenticated);
        return "index";
    }

    /**
     * Login page - shows login form.
     */
    @GetMapping("/login")
    public String login(Authentication authentication, Model model) {
        boolean isAuthenticated = authentication != null && authentication.isAuthenticated();
        model.addAttribute("isAuthenticated", isAuthenticated);
        return "login";
    }

    /**
     * Signup page - shows registration form.
     */
    @GetMapping("/signup")
    public String signup(Authentication authentication, Model model) {
        boolean isAuthenticated = authentication != null && authentication.isAuthenticated();
        model.addAttribute("isAuthenticated", isAuthenticated);
        return "signup";
    }

    /**
     * Dashboard page - shows user dashboard (requires authentication).
     */
    @GetMapping("/dashboard")
    public String dashboard(Authentication authentication, Model model) {
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

        var user = userService.findUserById(principal.getUserId());

        model.addAttribute("user", user.orElse(null));
        model.addAttribute("userName", principal.getEmail());
        model.addAttribute("isAuthenticated", true);
        return "dashboard";
    }

    /**
     * Profile page - shows user profile management (requires authentication).
     */
    @GetMapping("/profile")
    public String profile(Authentication authentication, Model model) {
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

        var user = userService.findUserById(principal.getUserId());

        model.addAttribute("user", user.orElse(null));
        model.addAttribute("userName", principal.getEmail());
        model.addAttribute("isAuthenticated", true);
        return "profile";
    }

    /**
     * Listings page - shows all active bike listings with search and filters.
     */
    @GetMapping("/listings")
    public String listings(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String q,
            @RequestParam(required = false) BikeType bikeType,
            @RequestParam(required = false) BigDecimal minPrice,
            @RequestParam(required = false) BigDecimal maxPrice,
            @RequestParam(required = false) ConditionTier conditionTier,
            @RequestParam(required = false) FrameMaterial frameMaterial,
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String state,
            Authentication authentication,
            Model model) {

        boolean isAuthenticated = authentication != null && authentication.isAuthenticated();

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<BikeListingSummaryDto> listings = bikeListingService.searchListings(
                q, bikeType, minPrice, maxPrice, conditionTier, frameMaterial, city, state, pageable);

        model.addAttribute("listings", listings);
        model.addAttribute("isAuthenticated", isAuthenticated);
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", listings.getTotalPages());
        model.addAttribute("searchTerm", q);
        model.addAttribute("selectedBikeType", bikeType);
        model.addAttribute("minPrice", minPrice);
        model.addAttribute("maxPrice", maxPrice);
        model.addAttribute("selectedCondition", conditionTier);
        model.addAttribute("selectedFrameMaterial", frameMaterial);
        model.addAttribute("city", city);
        model.addAttribute("state", state);

        // Add enum values for filters
        model.addAttribute("bikeTypes", Arrays.asList(BikeType.values()));
        model.addAttribute("conditionTiers", Arrays.asList(ConditionTier.values()));
        model.addAttribute("frameMaterials", Arrays.asList(FrameMaterial.values()));

        return "listings";
    }

    /**
     * Individual listing page - shows detailed bike listing.
     */
    @GetMapping("/listings/{slug}")
    public String listingDetail(@PathVariable String slug, Authentication authentication, Model model) {
        boolean isAuthenticated = authentication != null && authentication.isAuthenticated();

        UUID requestingUserId = null;
        if (isAuthenticated) {
            JwtAuthenticationFilter.UserPrincipal principal =
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();
            requestingUserId = principal.getUserId();
        }

        Optional<BikeListingResponseDto> listing = bikeListingService.findListingBySlug(slug, requestingUserId);

        if (listing.isEmpty()) {
            return "error/404";
        }

        model.addAttribute("listing", listing.get());
        model.addAttribute("isAuthenticated", isAuthenticated);
        model.addAttribute("canEdit", isAuthenticated &&
                listing.get().seller().id().equals(requestingUserId));

        return "listing-detail";
    }

    /**
     * Create listing page - shows form to create new bike listing (requires authentication).
     */
    @GetMapping("/listings/create")
    public String createListing(Authentication authentication, Model model) {
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

        var user = userService.findUserById(principal.getUserId());

        // Check if user can create listings
        if (user.isEmpty() || (!user.get().role().equals("SHOP") && !user.get().role().equals("USER"))) {
            return "error/403";
        }

        model.addAttribute("user", user.get());
        model.addAttribute("isAuthenticated", true);

        // Add enum values for form options
        model.addAttribute("frameMaterials", Arrays.asList(FrameMaterial.values()));
        model.addAttribute("bikeTypes", Arrays.asList(BikeType.values()));
        model.addAttribute("wheelSizes", Arrays.asList(WheelSize.values()));
        model.addAttribute("brakeTypes", Arrays.asList(BrakeType.values()));
        model.addAttribute("conditionTiers", Arrays.asList(ConditionTier.values()));
        model.addAttribute("photoTypes", Arrays.asList(PhotoType.values()));

        return "create-listing";
    }

    /**
     * My listings page - shows user's own listings (requires authentication).
     */
    @GetMapping("/my-listings")
    public String myListings(Authentication authentication, Model model) {
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

        var user = userService.findUserById(principal.getUserId());
        List<BikeListingSummaryDto> listings = bikeListingService.getListingsBySeller(principal.getUserId());

        model.addAttribute("user", user.orElse(null));
        model.addAttribute("listings", listings);
        model.addAttribute("isAuthenticated", true);

        return "my-listings";
    }
}
