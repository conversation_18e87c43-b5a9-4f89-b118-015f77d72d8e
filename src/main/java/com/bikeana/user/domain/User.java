package com.bikeana.user.domain;

import jakarta.persistence.*;

import java.util.UUID;

@Entity
@Table(name = "users") // Avoid PostgreSQL reserved keyword "user"
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    private String role; // SHOP, USER
    private String fullName;
    private String email;
    private String passwordHash;
    private String phone;
    private String status; // PENDING, ACTIVE

    // Default constructor
    public User() {
    }

    // Constructor for creating new users
    public User(String role, String fullName, String email, String passwordHash, String phone) {
        this.role = role;
        this.fullName = fullName;
        this.email = email;
        this.passwordHash = passwordHash;
        this.phone = phone;
        this.status = "PENDING"; // Default status
    }

    // Getters and Setters
    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPasswordHash() {
        return passwordHash;
    }

    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
