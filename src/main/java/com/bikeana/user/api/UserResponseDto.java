package com.bikeana.user.api;

import java.util.UUID;

/**
 * DTO for user response data.
 * This DTO excludes sensitive information like password hash.
 * 
 * @param id User's unique identifier
 * @param email User's email address
 * @param fullName User's full name
 * @param phone User's phone number (optional)
 * @param role User's role (SHOP, USER)
 * @param status User's status (PENDING, ACTIVE)
 */
public record UserResponseDto(
        UUID id,
        String email,
        String fullName,
        String phone,
        String role,
        String status
) {
}
