package com.bikeana.user.api;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * DTO for user signup requests.
 *
 * @param role User role - must be one of: SHOP, USER
 * @param email User's email address - must be valid email format
 * @param password User's password - minimum 8 characters
 * @param fullName User's full name - required
 * @param phone User's phone number - optional, but if provided must be valid format
 */
public record SignupRequestDto(

        @NotBlank(message = "Role is required")
        @Pattern(regexp = "^(SHOP|USER)$",
                message = "Role must be one of: SHOP, USER")
        String role,

        @NotBlank(message = "Email is required")
        @Email(message = "Email must be valid")
        @Size(max = 255, message = "Email must not exceed 255 characters")
        String email,

        @NotBlank(message = "Password is required")
        @Size(min = 8, max = 128, message = "Password must be between 8 and 128 characters")
        @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).*$",
                message = "Password must contain at least one lowercase letter, one uppercase letter, and one digit")
        String password,

        @NotBlank(message = "Full name is required")
        @Size(max = 255, message = "Full name must not exceed 255 characters")
        String fullName,

        @Pattern(regexp = "^$|^\\+?[1-9]\\d{1,14}$",
                message = "Phone number must be valid (E.164 format) or empty")
        String phone
) {
}
