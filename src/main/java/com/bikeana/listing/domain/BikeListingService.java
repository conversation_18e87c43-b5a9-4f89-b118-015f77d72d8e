package com.bikeana.listing.domain;

import com.bikeana.listing.api.*;
import com.bikeana.user.domain.User;
import com.bikeana.user.domain.UserRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.Normalizer;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service for bike listing business logic operations.
 * Handles CRUD operations, search, and privacy management.
 */
@Service
@Transactional
public class BikeListingService {

    private final BikeListingRepository bikeListingRepository;
    private final UserRepository userRepository;

    public BikeListingService(BikeListingRepository bikeListingRepository, UserRepository userRepository) {
        this.bikeListingRepository = bikeListingRepository;
        this.userRepository = userRepository;
    }

    /**
     * Creates a new bike listing.
     */
    public BikeListingResponseDto createListing(CreateBikeListingRequestDto request, UUID sellerId) {
        // Validate seller exists and has appropriate role
        User seller = userRepository.findById(sellerId)
                .orElseThrow(() -> new IllegalArgumentException("Seller not found"));
        
        if (!isValidSellerRole(seller.getRole())) {
            throw new IllegalArgumentException("User does not have permission to create listings");
        }

        // Check if serial number already exists
        if (bikeListingRepository.existsBySerialNumber(request.serialNumber())) {
            throw new IllegalArgumentException("A bike with this serial number already exists");
        }

        // Generate unique SEO slug
        String seoSlug = generateUniqueSlug(request.title());

        // Create listing entity
        BikeListing listing = BikeListingMapper.toEntity(request, seller, seoSlug);
        
        // Create location entity
        BikeListingLocation location = BikeListingMapper.toLocationEntity(request.location(), listing);
        listing.setLocation(location);

        // Save listing
        BikeListing savedListing = bikeListingRepository.save(listing);

        return BikeListingMapper.toResponseDto(savedListing, false, false);
    }

    /**
     * Finds a listing by ID with privacy controls.
     */
    @Transactional(readOnly = true)
    public Optional<BikeListingResponseDto> findListingById(UUID listingId, UUID requestingUserId) {
        Optional<BikeListing> listingOpt = bikeListingRepository.findById(listingId);
        
        if (listingOpt.isEmpty()) {
            return Optional.empty();
        }

        BikeListing listing = listingOpt.get();
        boolean showFullAddress = canViewFullAddress(listing, requestingUserId);
        boolean showSellerContact = canViewSellerContact(listing, requestingUserId);

        return Optional.of(BikeListingMapper.toResponseDto(listing, showFullAddress, showSellerContact));
    }

    /**
     * Finds a listing by SEO slug with privacy controls.
     */
    @Transactional(readOnly = true)
    public Optional<BikeListingResponseDto> findListingBySlug(String slug, UUID requestingUserId) {
        Optional<BikeListing> listingOpt = bikeListingRepository.findBySeoSlug(slug);
        
        if (listingOpt.isEmpty()) {
            return Optional.empty();
        }

        BikeListing listing = listingOpt.get();
        boolean showFullAddress = canViewFullAddress(listing, requestingUserId);
        boolean showSellerContact = canViewSellerContact(listing, requestingUserId);

        return Optional.of(BikeListingMapper.toResponseDto(listing, showFullAddress, showSellerContact));
    }

    /**
     * Gets all active listings with pagination.
     */
    @Transactional(readOnly = true)
    public Page<BikeListingSummaryDto> getActiveListings(Pageable pageable) {
        Page<BikeListing> listings = bikeListingRepository.findActiveListings(pageable);
        return listings.map(BikeListingMapper::toSummaryDto);
    }

    /**
     * Gets listings by seller ID.
     */
    @Transactional(readOnly = true)
    public List<BikeListingSummaryDto> getListingsBySeller(UUID sellerId) {
        List<BikeListing> listings = bikeListingRepository.findBySellerIdOrderByCreatedAtDesc(sellerId);
        return listings.stream()
                .map(BikeListingMapper::toSummaryDto)
                .collect(Collectors.toList());
    }

    /**
     * Searches listings by multiple criteria.
     */
    @Transactional(readOnly = true)
    public Page<BikeListingSummaryDto> searchListings(
            String searchTerm,
            BikeType bikeType,
            BigDecimal minPrice,
            BigDecimal maxPrice,
            ConditionTier conditionTier,
            FrameMaterial frameMaterial,
            String city,
            String state,
            Pageable pageable) {

        Page<BikeListing> listings;

        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            listings = bikeListingRepository.searchListings(searchTerm.trim(), pageable);
        } else if (city != null && state != null) {
            listings = bikeListingRepository.findByLocation(city, state, pageable);
        } else {
            listings = bikeListingRepository.findByMultipleCriteria(
                    bikeType, minPrice, maxPrice, conditionTier, frameMaterial, pageable);
        }

        return listings.map(BikeListingMapper::toSummaryDto);
    }

    /**
     * Updates listing status.
     */
    public Optional<BikeListingResponseDto> updateListingStatus(UUID listingId, ListingStatus status, UUID requestingUserId) {
        Optional<BikeListing> listingOpt = bikeListingRepository.findById(listingId);
        
        if (listingOpt.isEmpty()) {
            return Optional.empty();
        }

        BikeListing listing = listingOpt.get();
        
        // Check if user has permission to update this listing
        if (!listing.getSeller().getId().equals(requestingUserId)) {
            throw new IllegalArgumentException("User does not have permission to update this listing");
        }

        listing.setStatus(status);
        
        // If marking as sold, also update availability
        if (status == ListingStatus.SOLD) {
            listing.setAvailabilityStatus(AvailabilityStatus.SOLD);
        }

        BikeListing savedListing = bikeListingRepository.save(listing);
        return Optional.of(BikeListingMapper.toResponseDto(savedListing, false, false));
    }

    /**
     * Reserves a listing for a buyer.
     */
    public Optional<BikeListingResponseDto> reserveListing(UUID listingId, UUID buyerId, int reservationHours) {
        Optional<BikeListing> listingOpt = bikeListingRepository.findById(listingId);
        
        if (listingOpt.isEmpty()) {
            return Optional.empty();
        }

        BikeListing listing = listingOpt.get();
        
        // Check if listing is available
        if (listing.getAvailabilityStatus() != AvailabilityStatus.AVAILABLE) {
            throw new IllegalArgumentException("Listing is not available for reservation");
        }

        // Check if buyer is not the seller
        if (listing.getSeller().getId().equals(buyerId)) {
            throw new IllegalArgumentException("Seller cannot reserve their own listing");
        }

        listing.setAvailabilityStatus(AvailabilityStatus.RESERVED);
        listing.setReservedByUserId(buyerId);
        listing.setReservationExpiresAt(LocalDateTime.now().plusHours(reservationHours));

        BikeListing savedListing = bikeListingRepository.save(listing);
        return Optional.of(BikeListingMapper.toResponseDto(savedListing, false, false));
    }

    /**
     * Releases expired reservations.
     */
    @Transactional
    public void releaseExpiredReservations() {
        List<BikeListing> expiredReservations = bikeListingRepository.findExpiredReservations();
        
        for (BikeListing listing : expiredReservations) {
            listing.setAvailabilityStatus(AvailabilityStatus.AVAILABLE);
            listing.setReservedByUserId(null);
            listing.setReservationExpiresAt(null);
        }
        
        bikeListingRepository.saveAll(expiredReservations);
    }

    /**
     * Deletes a listing (soft delete by setting status to INACTIVE).
     */
    public boolean deleteListing(UUID listingId, UUID requestingUserId) {
        Optional<BikeListing> listingOpt = bikeListingRepository.findById(listingId);
        
        if (listingOpt.isEmpty()) {
            return false;
        }

        BikeListing listing = listingOpt.get();
        
        // Check if user has permission to delete this listing
        if (!listing.getSeller().getId().equals(requestingUserId)) {
            throw new IllegalArgumentException("User does not have permission to delete this listing");
        }

        listing.setStatus(ListingStatus.INACTIVE);
        listing.setAvailabilityStatus(AvailabilityStatus.UNAVAILABLE);
        bikeListingRepository.save(listing);
        
        return true;
    }

    /**
     * Checks if user can view full address (after escrow activation).
     */
    private boolean canViewFullAddress(BikeListing listing, UUID requestingUserId) {
        if (requestingUserId == null) {
            return false;
        }
        
        // Seller can always see full address
        if (listing.getSeller().getId().equals(requestingUserId)) {
            return true;
        }
        
        // Buyer can see full address if they have reserved the listing
        // In a real implementation, this would check escrow status
        return listing.getReservedByUserId() != null && 
               listing.getReservedByUserId().equals(requestingUserId);
    }

    /**
     * Checks if user can view seller contact information.
     */
    private boolean canViewSellerContact(BikeListing listing, UUID requestingUserId) {
        if (requestingUserId == null) {
            return false;
        }
        
        // Seller can always see their own contact
        if (listing.getSeller().getId().equals(requestingUserId)) {
            return true;
        }
        
        // Buyer can see contact if they have reserved the listing
        return listing.getReservedByUserId() != null && 
               listing.getReservedByUserId().equals(requestingUserId);
    }

    /**
     * Validates if user role can create listings.
     */
    private boolean isValidSellerRole(String role) {
        return "SHOP".equals(role) || "USER".equals(role);
    }

    /**
     * Generates a unique SEO slug from the title.
     */
    private String generateUniqueSlug(String title) {
        String baseSlug = createSlugFromTitle(title);
        String slug = baseSlug;
        int counter = 1;
        
        while (bikeListingRepository.existsBySeoSlug(slug)) {
            slug = baseSlug + "-" + counter;
            counter++;
        }
        
        return slug;
    }

    /**
     * Creates a URL-friendly slug from a title.
     */
    private String createSlugFromTitle(String title) {
        return Normalizer.normalize(title, Normalizer.Form.NFD)
                .replaceAll("[^\\p{ASCII}]", "")
                .toLowerCase()
                .replaceAll("[^a-z0-9\\s-]", "")
                .replaceAll("\\s+", "-")
                .replaceAll("-+", "-")
                .replaceAll("^-|-$", "");
    }
}
