{#include layout.qute.html}
    {#title}Photo Capture Demo - Bikeana{/title}
    {#content}
        <div class="min-h-screen bg-gray-50 py-8">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900">📸 Smart Photo Capture Demo</h1>
                    <p class="mt-2 text-gray-600">Experience our guided camera tool with ghost bike overlays</p>
                    <div class="mt-4 bg-blue-50 border border-blue-200 rounded-md p-4">
                        <p class="text-sm text-blue-700">
                            <strong>Demo Mode:</strong> This is a demonstration of the photo capture interface. 
                            In the real app, photos would be uploaded to your bike listing.
                        </p>
                    </div>
                </div>

                <!-- Photo Progress -->
                <div class="mb-6 bg-white rounded-lg shadow p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Photo Progress</h3>
                        <span id="photo-counter" class="text-sm text-gray-500">0 of 5 photos taken</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div class="mt-4 grid grid-cols-5 gap-2">
                        <div class="photo-type-indicator" data-type="DRIVE_SIDE_FULL">
                            <div class="w-full h-16 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center">
                                <span class="text-xs text-gray-500 text-center">Drive Side</span>
                            </div>
                        </div>
                        <div class="photo-type-indicator" data-type="NON_DRIVE_SIDE_FULL">
                            <div class="w-full h-16 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center">
                                <span class="text-xs text-gray-500 text-center">Non-Drive Side</span>
                            </div>
                        </div>
                        <div class="photo-type-indicator" data-type="DRIVETRAIN_CLOSEUP">
                            <div class="w-full h-16 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center">
                                <span class="text-xs text-gray-500 text-center">Drivetrain</span>
                            </div>
                        </div>
                        <div class="photo-type-indicator" data-type="BRAKE_CLOSEUP">
                            <div class="w-full h-16 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center">
                                <span class="text-xs text-gray-500 text-center">Brake</span>
                            </div>
                        </div>
                        <div class="photo-type-indicator" data-type="FRAME_DETAIL">
                            <div class="w-full h-16 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center">
                                <span class="text-xs text-gray-500 text-center">Frame Detail</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Camera Interface -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="p-6">
                        <div class="relative">
                            <!-- Camera View -->
                            <div id="camera-container" class="relative w-full max-w-2xl mx-auto">
                                <video id="camera-video" autoplay playsinline class="w-full h-auto rounded-lg bg-black"></video>
                                
                                <!-- Ghost Bike Overlay -->
                                <div id="ghost-overlay" class="absolute inset-0 pointer-events-none">
                                    <svg id="ghost-bike" class="w-full h-full opacity-40" viewBox="0 0 400 300" style="display: none;">
                                        <!-- Drive Side Full Bike Ghost -->
                                        <g id="ghost-drive-side" style="display: none;">
                                            <path d="M50 200 L350 200 M100 200 L100 150 L200 120 L250 150 L250 200 M150 150 L200 150 M200 120 L200 100 M180 100 L220 100" 
                                                  stroke="white" stroke-width="3" fill="none"/>
                                            <circle cx="100" cy="200" r="30" stroke="white" stroke-width="3" fill="none"/>
                                            <circle cx="300" cy="200" r="30" stroke="white" stroke-width="3" fill="none"/>
                                            <text x="200" y="280" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Align your bike with this outline</text>
                                        </g>
                                        
                                        <!-- Non-Drive Side Full Bike Ghost -->
                                        <g id="ghost-non-drive-side" style="display: none;">
                                            <path d="M50 200 L350 200 M100 200 L100 150 L200 120 L250 150 L250 200 M150 150 L200 150 M200 120 L200 100 M180 100 L220 100" 
                                                  stroke="white" stroke-width="3" fill="none"/>
                                            <circle cx="100" cy="200" r="30" stroke="white" stroke-width="3" fill="none"/>
                                            <circle cx="300" cy="200" r="30" stroke="white" stroke-width="3" fill="none"/>
                                            <text x="200" y="280" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Show the opposite side</text>
                                        </g>
                                        
                                        <!-- Drivetrain Closeup Ghost -->
                                        <g id="ghost-drivetrain" style="display: none;">
                                            <circle cx="200" cy="150" r="50" stroke="white" stroke-width="4" fill="none"/>
                                            <path d="M150 150 L250 150 M200 100 L200 200" stroke="white" stroke-width="3" fill="none"/>
                                            <text x="200" y="230" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Center the drivetrain in the circle</text>
                                        </g>
                                        
                                        <!-- Brake Closeup Ghost -->
                                        <g id="ghost-brake" style="display: none;">
                                            <rect x="170" y="120" width="60" height="60" stroke="white" stroke-width="4" fill="none"/>
                                            <path d="M180 140 L220 140 M180 160 L220 160" stroke="white" stroke-width="3" fill="none"/>
                                            <text x="200" y="210" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Frame the brake system</text>
                                        </g>
                                        
                                        <!-- Frame Detail Ghost -->
                                        <g id="ghost-frame" style="display: none;">
                                            <path d="M150 100 L250 100 L200 200 Z" stroke="white" stroke-width="4" fill="none"/>
                                            <text x="200" y="230" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Capture frame details</text>
                                        </g>
                                    </svg>
                                </div>
                                
                                <!-- Capture Button -->
                                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                                    <button id="capture-btn" class="w-20 h-20 bg-white rounded-full border-4 border-gray-300 hover:border-blue-500 transition-colors duration-200 flex items-center justify-center shadow-lg">
                                        <div class="w-16 h-16 bg-gray-200 rounded-full"></div>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Camera Not Available -->
                            <div id="no-camera" class="hidden text-center py-12">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.93 7H20a2 2 0 012 2v9a2 2 0 01-2 2H4a2 2 0 01-2-2V9z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">Camera not available</h3>
                                <p class="mt-1 text-sm text-gray-500">Please allow camera access to try the demo.</p>
                                <div class="mt-6">
                                    <button id="retry-camera-btn" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                        Try Again
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Photo Type Instructions -->
                <div id="photo-instructions" class="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 id="instruction-title" class="text-sm font-medium text-blue-800">Drive-side full-bike view</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <p id="instruction-text">Position your bike so the chain and gears are visible. Align the ghost outline with your bike and tap capture.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Captured Photos Preview -->
                <div id="captured-photos" class="mt-6 bg-white rounded-lg shadow p-6" style="display: none;">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Captured Photos (Demo)</h3>
                    <div id="photo-grid" class="grid grid-cols-2 md:grid-cols-5 gap-4"></div>
                </div>

                <!-- Action Buttons -->
                <div class="mt-6 flex justify-between">
                    <a href="/" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        ← Back to Home
                    </a>
                    
                    <div class="flex space-x-3">
                        <button id="retake-btn" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" style="display: none;">
                            Retake Current
                        </button>
                        <button id="demo-complete-btn" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700" style="display: none;">
                            Demo Complete! 🎉
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hidden canvas for photo capture -->
        <canvas id="capture-canvas" style="display: none;"></canvas>

        <script>
            // Demo version of photo capture functionality
            class PhotoCaptureDemo {
                constructor() {
                    this.currentPhotoIndex = 0;
                    this.capturedPhotos = [];
                    this.photoTypes = [
                        { type: 'DRIVE_SIDE_FULL', title: 'Drive-side full-bike view', instruction: 'Position your bike so the chain and gears are visible. Align the ghost outline with your bike and tap capture.' },
                        { type: 'NON_DRIVE_SIDE_FULL', title: 'Non-drive-side full-bike view', instruction: 'Show the opposite side of your bike. Align the ghost outline and capture.' },
                        { type: 'DRIVETRAIN_CLOSEUP', title: 'Drivetrain close-up', instruction: 'Focus on the chain, cassette, and derailleur. Center them in the ghost circle.' },
                        { type: 'BRAKE_CLOSEUP', title: 'Brake close-up', instruction: 'Capture a clear view of the brake system. Align with the ghost rectangle.' },
                        { type: 'FRAME_DETAIL', title: 'Frame detail close-up', instruction: 'Show frame details, welds, or any unique features. Use the ghost triangle as a guide.' }
                    ];
                    this.stream = null;
                    this.video = document.getElementById('camera-video');
                    this.canvas = document.getElementById('capture-canvas');
                    this.ctx = this.canvas.getContext('2d');
                    
                    this.init();
                }

                async init() {
                    this.updateInstructions();
                    this.updateGhostOverlay();
                    await this.startCamera();
                    this.bindEvents();
                }

                async startCamera() {
                    try {
                        this.stream = await navigator.mediaDevices.getUserMedia({ 
                            video: { 
                                facingMode: 'environment',
                                width: { ideal: 1280 },
                                height: { ideal: 720 }
                            } 
                        });
                        this.video.srcObject = this.stream;
                        document.getElementById('camera-container').style.display = 'block';
                        document.getElementById('no-camera').style.display = 'none';
                    } catch (error) {
                        console.error('Camera access denied:', error);
                        document.getElementById('camera-container').style.display = 'none';
                        document.getElementById('no-camera').style.display = 'block';
                    }
                }

                bindEvents() {
                    document.getElementById('capture-btn').addEventListener('click', () => this.capturePhoto());
                    document.getElementById('retake-btn').addEventListener('click', () => this.retakePhoto());
                    document.getElementById('demo-complete-btn').addEventListener('click', () => this.completeDemo());
                    document.getElementById('retry-camera-btn').addEventListener('click', () => this.startCamera());
                }

                updateInstructions() {
                    const currentPhoto = this.photoTypes[this.currentPhotoIndex];
                    document.getElementById('instruction-title').textContent = currentPhoto.title;
                    document.getElementById('instruction-text').textContent = currentPhoto.instruction;
                }

                updateGhostOverlay() {
                    document.querySelectorAll('#ghost-bike g').forEach(g => g.style.display = 'none');
                    
                    const currentType = this.photoTypes[this.currentPhotoIndex].type;
                    const ghostMap = {
                        'DRIVE_SIDE_FULL': 'ghost-drive-side',
                        'NON_DRIVE_SIDE_FULL': 'ghost-non-drive-side',
                        'DRIVETRAIN_CLOSEUP': 'ghost-drivetrain',
                        'BRAKE_CLOSEUP': 'ghost-brake',
                        'FRAME_DETAIL': 'ghost-frame'
                    };
                    
                    const ghostId = ghostMap[currentType];
                    if (ghostId) {
                        document.getElementById('ghost-bike').style.display = 'block';
                        document.getElementById(ghostId).style.display = 'block';
                    }
                }

                capturePhoto() {
                    if (!this.stream) return;

                    this.canvas.width = this.video.videoWidth;
                    this.canvas.height = this.video.videoHeight;
                    this.ctx.drawImage(this.video, 0, 0);
                    
                    this.canvas.toBlob((blob) => {
                        const photoData = {
                            blob: blob,
                            type: this.photoTypes[this.currentPhotoIndex].type,
                            title: this.photoTypes[this.currentPhotoIndex].title,
                            url: URL.createObjectURL(blob)
                        };
                        
                        this.capturedPhotos[this.currentPhotoIndex] = photoData;
                        this.updateProgress();
                        this.showCapturedPhoto(photoData);
                        this.nextPhoto();
                    }, 'image/jpeg', 0.8);
                }

                showCapturedPhoto(photoData) {
                    const indicator = document.querySelector(`[data-type="${photoData.type}"]`);
                    if (indicator) {
                        indicator.innerHTML = `<img src="${photoData.url}" class="w-full h-16 object-cover rounded border-2 border-green-500">`;
                    }
                    
                    document.getElementById('captured-photos').style.display = 'block';
                    this.updatePhotoGrid();
                }

                updatePhotoGrid() {
                    const grid = document.getElementById('photo-grid');
                    grid.innerHTML = '';
                    
                    this.capturedPhotos.forEach((photo, index) => {
                        if (photo) {
                            const div = document.createElement('div');
                            div.className = 'relative';
                            div.innerHTML = `
                                <img src="${photo.url}" class="w-full h-24 object-cover rounded">
                                <p class="text-xs text-gray-600 mt-1">${photo.title}</p>
                            `;
                            grid.appendChild(div);
                        }
                    });
                }

                nextPhoto() {
                    this.currentPhotoIndex++;
                    
                    if (this.currentPhotoIndex >= this.photoTypes.length) {
                        this.completeCapture();
                    } else {
                        this.updateInstructions();
                        this.updateGhostOverlay();
                        document.getElementById('retake-btn').style.display = 'inline-flex';
                    }
                }

                retakePhoto() {
                    if (this.currentPhotoIndex > 0) {
                        this.currentPhotoIndex--;
                        this.updateInstructions();
                        this.updateGhostOverlay();
                        
                        if (this.capturedPhotos[this.currentPhotoIndex]) {
                            URL.revokeObjectURL(this.capturedPhotos[this.currentPhotoIndex].url);
                            this.capturedPhotos[this.currentPhotoIndex] = null;
                        }
                        
                        this.updateProgress();
                        this.updatePhotoGrid();
                        
                        const currentType = this.photoTypes[this.currentPhotoIndex].type;
                        const indicator = document.querySelector(`[data-type="${currentType}"]`);
                        if (indicator) {
                            const titleFirstWord = this.photoTypes[this.currentPhotoIndex].title.split(' ')[0];
                            indicator.innerHTML = `
                                <div class="w-full h-16 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center">
                                    <span class="text-xs text-gray-500 text-center">${titleFirstWord}</span>
                                </div>
                            `;
                        }
                    }
                }

                completeCapture() {
                    document.getElementById('ghost-bike').style.display = 'none';
                    document.getElementById('capture-btn').style.display = 'none';
                    document.getElementById('retake-btn').style.display = 'inline-flex';
                    document.getElementById('demo-complete-btn').style.display = 'inline-flex';
                    
                    document.getElementById('instruction-title').textContent = 'Demo Complete! 🎉';
                    document.getElementById('instruction-text').textContent = 'You\'ve experienced our smart photo capture system. In the real app, these photos would be uploaded to your bike listing automatically.';
                }

                updateProgress() {
                    const completed = this.capturedPhotos.filter(p => p !== null && p !== undefined).length;
                    const percentage = (completed / this.photoTypes.length) * 100;
                    
                    document.getElementById('progress-bar').style.width = `${percentage}%`;
                    document.getElementById('photo-counter').textContent = `${completed} of ${this.photoTypes.length} photos taken`;
                }

                completeDemo() {
                    alert('🎉 Demo complete! In the real app, you would now create an account to list your bike with these photos.');
                }

                cleanup() {
                    if (this.stream) {
                        this.stream.getTracks().forEach(track => track.stop());
                    }
                    this.capturedPhotos.forEach(photo => {
                        if (photo && photo.url) {
                            URL.revokeObjectURL(photo.url);
                        }
                    });
                }
            }

            document.addEventListener('DOMContentLoaded', () => {
                window.photoCaptureDemo = new PhotoCaptureDemo();
            });

            window.addEventListener('beforeunload', () => {
                if (window.photoCaptureDemo) {
                    window.photoCaptureDemo.cleanup();
                }
            });
        </script>
    {/content}
{/include}
