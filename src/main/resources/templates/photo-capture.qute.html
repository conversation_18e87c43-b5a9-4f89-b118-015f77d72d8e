{#include layout.qute.html}
    {#title}Take Photos - Bikeana{/title}
    {#content}
        <div class="min-h-screen bg-gray-50 py-8">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900">Take Photos for Your Listing</h1>
                    <p class="mt-2 text-gray-600">Capture high-quality photos using our guided camera tool</p>
                </div>

                <!-- Photo Progress -->
                <div class="mb-6 bg-white rounded-lg shadow p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Photo Progress</h3>
                        <span id="photo-counter" class="text-sm text-gray-500">0 of 5 photos taken</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div class="mt-4 grid grid-cols-5 gap-2">
                        <div class="photo-type-indicator" data-type="DRIVE_SIDE_FULL">
                            <div class="w-full h-16 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center">
                                <span class="text-xs text-gray-500 text-center">Drive Side</span>
                            </div>
                        </div>
                        <div class="photo-type-indicator" data-type="NON_DRIVE_SIDE_FULL">
                            <div class="w-full h-16 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center">
                                <span class="text-xs text-gray-500 text-center">Non-Drive Side</span>
                            </div>
                        </div>
                        <div class="photo-type-indicator" data-type="DRIVETRAIN_CLOSEUP">
                            <div class="w-full h-16 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center">
                                <span class="text-xs text-gray-500 text-center">Drivetrain</span>
                            </div>
                        </div>
                        <div class="photo-type-indicator" data-type="BRAKE_CLOSEUP">
                            <div class="w-full h-16 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center">
                                <span class="text-xs text-gray-500 text-center">Brake</span>
                            </div>
                        </div>
                        <div class="photo-type-indicator" data-type="FRAME_DETAIL">
                            <div class="w-full h-16 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center">
                                <span class="text-xs text-gray-500 text-center">Frame Detail</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Camera Interface -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="p-6">
                        <div class="relative">
                            <!-- Camera View -->
                            <div id="camera-container" class="relative w-full max-w-2xl mx-auto">
                                <video id="camera-video" autoplay playsinline class="w-full h-auto rounded-lg bg-black"></video>
                                
                                <!-- Ghost Bike Overlay -->
                                <div id="ghost-overlay" class="absolute inset-0 pointer-events-none">
                                    <svg id="ghost-bike" class="w-full h-full opacity-30" viewBox="0 0 400 300" style="display: none;">
                                        <!-- Drive Side Full Bike Ghost -->
                                        <g id="ghost-drive-side" style="display: none;">
                                            <path d="M50 200 L350 200 M100 200 L100 150 L200 120 L250 150 L250 200 M150 150 L200 150 M200 120 L200 100 M180 100 L220 100" 
                                                  stroke="white" stroke-width="2" fill="none"/>
                                            <circle cx="100" cy="200" r="30" stroke="white" stroke-width="2" fill="none"/>
                                            <circle cx="300" cy="200" r="30" stroke="white" stroke-width="2" fill="none"/>
                                        </g>
                                        
                                        <!-- Non-Drive Side Full Bike Ghost -->
                                        <g id="ghost-non-drive-side" style="display: none;">
                                            <path d="M50 200 L350 200 M100 200 L100 150 L200 120 L250 150 L250 200 M150 150 L200 150 M200 120 L200 100 M180 100 L220 100" 
                                                  stroke="white" stroke-width="2" fill="none"/>
                                            <circle cx="100" cy="200" r="30" stroke="white" stroke-width="2" fill="none"/>
                                            <circle cx="300" cy="200" r="30" stroke="white" stroke-width="2" fill="none"/>
                                        </g>
                                        
                                        <!-- Drivetrain Closeup Ghost -->
                                        <g id="ghost-drivetrain" style="display: none;">
                                            <circle cx="200" cy="150" r="40" stroke="white" stroke-width="3" fill="none"/>
                                            <path d="M160 150 L240 150 M200 110 L200 190" stroke="white" stroke-width="2" fill="none"/>
                                        </g>
                                        
                                        <!-- Brake Closeup Ghost -->
                                        <g id="ghost-brake" style="display: none;">
                                            <rect x="180" y="130" width="40" height="40" stroke="white" stroke-width="2" fill="none"/>
                                            <path d="M190 140 L210 140 M190 160 L210 160" stroke="white" stroke-width="2" fill="none"/>
                                        </g>
                                        
                                        <!-- Frame Detail Ghost -->
                                        <g id="ghost-frame" style="display: none;">
                                            <path d="M150 100 L250 100 L200 200 Z" stroke="white" stroke-width="3" fill="none"/>
                                        </g>
                                    </svg>
                                </div>
                                
                                <!-- Capture Button -->
                                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                                    <button id="capture-btn" class="w-16 h-16 bg-white rounded-full border-4 border-gray-300 hover:border-blue-500 transition-colors duration-200 flex items-center justify-center">
                                        <div class="w-12 h-12 bg-gray-200 rounded-full"></div>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Camera Not Available -->
                            <div id="no-camera" class="hidden text-center py-12">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.93 7H20a2 2 0 012 2v9a2 2 0 01-2 2H4a2 2 0 01-2-2V9z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">Camera not available</h3>
                                <p class="mt-1 text-sm text-gray-500">Please allow camera access or use file upload instead.</p>
                                <div class="mt-6">
                                    <button id="file-upload-btn" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                        Upload Files Instead
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Photo Type Instructions -->
                <div id="photo-instructions" class="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 id="instruction-title" class="text-sm font-medium text-blue-800">Drive-side full-bike view</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <p id="instruction-text">Position your bike so the chain and gears are visible. Align the ghost outline with your bike and tap capture.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Captured Photos Preview -->
                <div id="captured-photos" class="mt-6 bg-white rounded-lg shadow p-6" style="display: none;">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Captured Photos</h3>
                    <div id="photo-grid" class="grid grid-cols-2 md:grid-cols-5 gap-4"></div>
                </div>

                <!-- Action Buttons -->
                <div class="mt-6 flex justify-between">
                    <button id="skip-camera-btn" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Skip Camera, Upload Files
                    </button>
                    
                    <div class="flex space-x-3">
                        <button id="retake-btn" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" style="display: none;">
                            Retake Current
                        </button>
                        <button id="finish-btn" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700" style="display: none;">
                            Finish & Upload Photos
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hidden canvas for photo capture -->
        <canvas id="capture-canvas" style="display: none;"></canvas>
        
        <!-- Hidden file input for fallback -->
        <input type="file" id="file-input" multiple accept="image/*" style="display: none;">

        <script>
            class PhotoCapture {
                constructor() {
                    this.currentPhotoIndex = 0;
                    this.capturedPhotos = [];
                    this.photoTypes = [
                        { type: 'DRIVE_SIDE_FULL', title: 'Drive-side full-bike view', instruction: 'Position your bike so the chain and gears are visible. Align the ghost outline with your bike and tap capture.' },
                        { type: 'NON_DRIVE_SIDE_FULL', title: 'Non-drive-side full-bike view', instruction: 'Show the opposite side of your bike. Align the ghost outline and capture.' },
                        { type: 'DRIVETRAIN_CLOSEUP', title: 'Drivetrain close-up', instruction: 'Focus on the chain, cassette, and derailleur. Center them in the ghost circle.' },
                        { type: 'BRAKE_CLOSEUP', title: 'Brake close-up', instruction: 'Capture a clear view of the brake system. Align with the ghost rectangle.' },
                        { type: 'FRAME_DETAIL', title: 'Frame detail close-up', instruction: 'Show frame details, welds, or any unique features. Use the ghost triangle as a guide.' }
                    ];
                    this.stream = null;
                    this.video = document.getElementById('camera-video');
                    this.canvas = document.getElementById('capture-canvas');
                    this.ctx = this.canvas.getContext('2d');

                    this.init();
                }

                async init() {
                    this.updateInstructions();
                    this.updateGhostOverlay();
                    await this.startCamera();
                    this.bindEvents();
                }

                async startCamera() {
                    try {
                        this.stream = await navigator.mediaDevices.getUserMedia({
                            video: {
                                facingMode: 'environment', // Use back camera on mobile
                                width: { ideal: 1280 },
                                height: { ideal: 720 }
                            }
                        });
                        this.video.srcObject = this.stream;
                        document.getElementById('camera-container').style.display = 'block';
                        document.getElementById('no-camera').style.display = 'none';
                    } catch (error) {
                        console.error('Camera access denied:', error);
                        document.getElementById('camera-container').style.display = 'none';
                        document.getElementById('no-camera').style.display = 'block';
                    }
                }

                bindEvents() {
                    document.getElementById('capture-btn').addEventListener('click', () => this.capturePhoto());
                    document.getElementById('retake-btn').addEventListener('click', () => this.retakePhoto());
                    document.getElementById('finish-btn').addEventListener('click', () => this.finishCapture());
                    document.getElementById('skip-camera-btn').addEventListener('click', () => this.skipToFileUpload());
                    document.getElementById('file-upload-btn').addEventListener('click', () => this.skipToFileUpload());
                    document.getElementById('file-input').addEventListener('change', (e) => this.handleFileUpload(e));
                }

                updateInstructions() {
                    const currentPhoto = this.photoTypes[this.currentPhotoIndex];
                    document.getElementById('instruction-title').textContent = currentPhoto.title;
                    document.getElementById('instruction-text').textContent = currentPhoto.instruction;
                }

                updateGhostOverlay() {
                    // Hide all ghost overlays
                    document.querySelectorAll('#ghost-bike g').forEach(g => g.style.display = 'none');

                    // Show current ghost overlay
                    const currentType = this.photoTypes[this.currentPhotoIndex].type;
                    const ghostMap = {
                        'DRIVE_SIDE_FULL': 'ghost-drive-side',
                        'NON_DRIVE_SIDE_FULL': 'ghost-non-drive-side',
                        'DRIVETRAIN_CLOSEUP': 'ghost-drivetrain',
                        'BRAKE_CLOSEUP': 'ghost-brake',
                        'FRAME_DETAIL': 'ghost-frame'
                    };

                    const ghostId = ghostMap[currentType];
                    if (ghostId) {
                        document.getElementById('ghost-bike').style.display = 'block';
                        document.getElementById(ghostId).style.display = 'block';
                    }
                }

                capturePhoto() {
                    if (!this.stream) return;

                    // Set canvas size to match video
                    this.canvas.width = this.video.videoWidth;
                    this.canvas.height = this.video.videoHeight;

                    // Draw video frame to canvas
                    this.ctx.drawImage(this.video, 0, 0);

                    // Convert to blob
                    this.canvas.toBlob((blob) => {
                        const photoData = {
                            blob: blob,
                            type: this.photoTypes[this.currentPhotoIndex].type,
                            title: this.photoTypes[this.currentPhotoIndex].title,
                            url: URL.createObjectURL(blob)
                        };

                        this.capturedPhotos[this.currentPhotoIndex] = photoData;
                        this.updateProgress();
                        this.showCapturedPhoto(photoData);
                        this.nextPhoto();
                    }, 'image/jpeg', 0.8);
                }

                showCapturedPhoto(photoData) {
                    const indicator = document.querySelector(`[data-type="${photoData.type}"]`);
                    if (indicator) {
                        indicator.innerHTML = `<img src="${photoData.url}" class="w-full h-16 object-cover rounded border-2 border-green-500">`;
                    }

                    // Show captured photos section
                    document.getElementById('captured-photos').style.display = 'block';
                    this.updatePhotoGrid();
                }

                updatePhotoGrid() {
                    const grid = document.getElementById('photo-grid');
                    grid.innerHTML = '';

                    this.capturedPhotos.forEach((photo, index) => {
                        if (photo) {
                            const div = document.createElement('div');
                            div.className = 'relative';
                            div.innerHTML = `
                                <img src="${photo.url}" class="w-full h-24 object-cover rounded">
                                <p class="text-xs text-gray-600 mt-1">${photo.title}</p>
                            `;
                            grid.appendChild(div);
                        }
                    });
                }

                nextPhoto() {
                    this.currentPhotoIndex++;

                    if (this.currentPhotoIndex >= this.photoTypes.length) {
                        this.completeCapture();
                    } else {
                        this.updateInstructions();
                        this.updateGhostOverlay();
                        document.getElementById('retake-btn').style.display = 'inline-flex';
                    }
                }

                retakePhoto() {
                    if (this.currentPhotoIndex > 0) {
                        this.currentPhotoIndex--;
                        this.updateInstructions();
                        this.updateGhostOverlay();

                        // Remove the photo from captured photos
                        if (this.capturedPhotos[this.currentPhotoIndex]) {
                            URL.revokeObjectURL(this.capturedPhotos[this.currentPhotoIndex].url);
                            this.capturedPhotos[this.currentPhotoIndex] = null;
                        }

                        this.updateProgress();
                        this.updatePhotoGrid();

                        // Reset indicator
                        const currentType = this.photoTypes[this.currentPhotoIndex].type;
                        const indicator = document.querySelector(`[data-type="${currentType}"]`);
                        if (indicator) {
                            indicator.innerHTML = `
                                <div class="w-full h-16 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center">
                                    <span class="text-xs text-gray-500 text-center">${this.photoTypes[this.currentPhotoIndex].title.split(' ')[0]}</span>
                                </div>
                            `;
                        }
                    }
                }

                completeCapture() {
                    document.getElementById('ghost-bike').style.display = 'none';
                    document.getElementById('capture-btn').style.display = 'none';
                    document.getElementById('retake-btn').style.display = 'inline-flex';
                    document.getElementById('finish-btn').style.display = 'inline-flex';

                    document.getElementById('instruction-title').textContent = 'All photos captured!';
                    document.getElementById('instruction-text').textContent = 'Review your photos and click "Finish & Upload Photos" to complete your listing.';
                }

                updateProgress() {
                    const completed = this.capturedPhotos.filter(p => p !== null && p !== undefined).length;
                    const percentage = (completed / this.photoTypes.length) * 100;

                    document.getElementById('progress-bar').style.width = `${percentage}%`;
                    document.getElementById('photo-counter').textContent = `${completed} of ${this.photoTypes.length} photos taken`;
                }

                async finishCapture() {
                    const listingId = new URLSearchParams(window.location.search).get('listingId');
                    if (!listingId) {
                        alert('Error: No listing ID found');
                        return;
                    }

                    const formData = new FormData();
                    const photoTypes = [];

                    this.capturedPhotos.forEach((photo, index) => {
                        if (photo) {
                            formData.append('files', photo.blob, `photo_${index}.jpg`);
                            photoTypes.push(photo.type);
                        }
                    });

                    photoTypes.forEach(type => formData.append('photoTypes', type));
                    formData.append('primaryPhotoIndex', '0');

                    try {
                        document.getElementById('finish-btn').disabled = true;
                        document.getElementById('finish-btn').textContent = 'Uploading...';

                        const response = await fetch(`/api/listings/${listingId}/photos`, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
                            }
                        });

                        if (response.ok) {
                            window.location.href = `/listings/${listingId}`;
                        } else {
                            const error = await response.json();
                            alert(`Upload failed: ${error.message || 'Unknown error'}`);
                        }
                    } catch (error) {
                        console.error('Upload error:', error);
                        alert('Upload failed. Please try again.');
                    } finally {
                        document.getElementById('finish-btn').disabled = false;
                        document.getElementById('finish-btn').textContent = 'Finish & Upload Photos';
                    }
                }

                skipToFileUpload() {
                    document.getElementById('file-input').click();
                }

                handleFileUpload(event) {
                    const files = Array.from(event.target.files);
                    if (files.length < 5) {
                        alert('Please select at least 5 photos');
                        return;
                    }

                    // Redirect to traditional upload page or handle file upload
                    const listingId = new URLSearchParams(window.location.search).get('listingId');
                    window.location.href = `/listings/${listingId}/upload-photos`;
                }

                cleanup() {
                    if (this.stream) {
                        this.stream.getTracks().forEach(track => track.stop());
                    }
                    this.capturedPhotos.forEach(photo => {
                        if (photo && photo.url) {
                            URL.revokeObjectURL(photo.url);
                        }
                    });
                }
            }

            // Initialize when page loads
            document.addEventListener('DOMContentLoaded', () => {
                window.photoCapture = new PhotoCapture();
            });

            // Cleanup when page unloads
            window.addEventListener('beforeunload', () => {
                if (window.photoCapture) {
                    window.photoCapture.cleanup();
                }
            });
        </script>
    {/content}
{/include}
