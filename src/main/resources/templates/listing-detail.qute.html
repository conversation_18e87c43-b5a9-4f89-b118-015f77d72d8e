{#include layout.qute.html}
    {#title}{listing.title} - Bikeana{/title}
    {#content}
        <div class="min-h-screen bg-gray-50">
            <!-- <PERSON><PERSON> Container -->
            <div id="alert-container" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6"></div>

            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <!-- Breadcrumb -->
                <nav class="flex mb-8" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="/" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                                Home
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <a href="/listings" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">Listings</a>
                            </div>
                        </li>
                        <li aria-current="page">
                            <div class="flex items-center">
                                <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">{listing.title}</span>
                            </div>
                        </li>
                    </ol>
                </nav>

                <div class="lg:grid lg:grid-cols-2 lg:gap-x-8 lg:items-start">
                    <!-- Image gallery -->
                    <div class="flex flex-col-reverse">
                        <!-- Image selector -->
                        {#if listing.photos().size() > 1}
                            <div class="hidden mt-6 w-full max-w-2xl mx-auto sm:block lg:max-w-none">
                                <div class="grid grid-cols-4 gap-6">
                                    {#for photo in listing.photos()}
                                        <button type="button"
                                                onclick="document.getElementById('main-image').src='/uploads/{photo.filePath()}'"
                                                class="relative h-24 bg-white rounded-md flex items-center justify-center text-sm font-medium uppercase text-gray-900 cursor-pointer hover:bg-gray-50 focus:outline-none focus:ring focus:ring-offset-4 focus:ring-blue-500">
                                            <span class="sr-only">{photo.altText()}</span>
                                            <span class="absolute inset-0 rounded-md overflow-hidden">
                                                <img src="/uploads/{photo.filePath()}"
                                                     alt="{photo.altText()}"
                                                     class="w-full h-full object-center object-cover">
                                            </span>
                                        </button>
                                    {/for}
                                </div>
                            </div>
                        {/if}

                        <!-- Main image -->
                        <div class="w-full aspect-w-1 aspect-h-1">
                            {#if listing.primaryPhoto()}
                                <img id="main-image"
                                     src="/uploads/{listing.primaryPhoto().filePath()}"
                                     alt="{listing.primaryPhoto().altText()}"
                                     class="w-full h-full object-center object-cover sm:rounded-lg">
                            {#else}
                                <div class="w-full h-96 bg-gray-300 flex items-center justify-center sm:rounded-lg">
                                    <svg class="h-24 w-24 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                            {/if}
                        </div>
                    </div>

                    <!-- Product info -->
                    <div class="mt-10 px-4 sm:px-0 sm:mt-16 lg:mt-0">
                        <h1 class="text-3xl font-extrabold tracking-tight text-gray-900">{listing.title()}</h1>

                        <div class="mt-3">
                            <h2 class="sr-only">Product information</h2>
                            <p class="text-3xl text-gray-900">${listing.askingPrice()}
                                {#if listing.negotiable()}
                                    <span class="text-lg text-gray-500 font-normal">OBO</span>
                                {/if}
                            </p>
                        </div>

                        <!-- Bike details -->
                        <div class="mt-6">
                            <h3 class="text-sm font-medium text-gray-900">Bike Details</h3>
                            <div class="mt-4 space-y-2">
                                <p class="text-sm text-gray-600"><span class="font-medium">Year:</span> {listing.year()}</p>
                                <p class="text-sm text-gray-600"><span class="font-medium">Brand:</span> {listing.brand()}</p>
                                <p class="text-sm text-gray-600"><span class="font-medium">Model:</span> {listing.model()}</p>
                                <p class="text-sm text-gray-600"><span class="font-medium">Type:</span> {listing.bikeType().displayName()}</p>
                                <p class="text-sm text-gray-600"><span class="font-medium">Frame Material:</span> {listing.frameMaterial().displayName()}</p>
                                <p class="text-sm text-gray-600"><span class="font-medium">Frame Size:</span> {listing.frameSize()}</p>
                                <p class="text-sm text-gray-600"><span class="font-medium">Wheel Size:</span> {listing.wheelSize().displayName()}</p>
                                <p class="text-sm text-gray-600"><span class="font-medium">Drivetrain:</span> {listing.drivetrainSpeeds()} speeds</p>
                                <p class="text-sm text-gray-600"><span class="font-medium">Groupset:</span> {listing.groupsetBrand()}</p>
                                <p class="text-sm text-gray-600"><span class="font-medium">Brakes:</span> {listing.brakeType().displayName()}</p>
                                <p class="text-sm text-gray-600"><span class="font-medium">Color:</span> {listing.color()}</p>
                            </div>
                        </div>

                        <!-- Condition -->
                        <div class="mt-6">
                            <h3 class="text-sm font-medium text-gray-900">Condition</h3>
                            <div class="mt-4">
                                <span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                    {listing.conditionTier().displayName()}
                                </span>
                                <p class="mt-2 text-sm text-gray-600">{listing.conditionNotes()}</p>
                            </div>
                        </div>

                        <!-- Location -->
                        {#if listing.location()}
                            <div class="mt-6">
                                <h3 class="text-sm font-medium text-gray-900">Location</h3>
                                <p class="mt-2 text-sm text-gray-600">
                                    {#if listing.location().streetAddress()}
                                        {listing.location().streetAddress()}, {listing.location().city()}, {listing.location().state()} {listing.location().zipCode()}
                                    {#else}
                                        {listing.location().publicDisplayAddress()}
                                        <span class="text-yellow-600 font-medium ml-2">
                                            (Full address shown after purchase)
                                        </span>
                                    {/if}
                                </p>
                            </div>
                        {/if}

                        <!-- Seller info -->
                        {#if listing.seller()}
                            <div class="mt-6">
                                <h3 class="text-sm font-medium text-gray-900">Seller</h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-600">
                                        <span class="font-medium">{listing.seller().fullName()}</span>
                                        {#if listing.seller().role() == 'SHOP'}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 ml-2">
                                                Bike Shop
                                            </span>
                                        {/if}
                                    </p>
                                    {#if listing.seller().email()}
                                        <p class="text-sm text-gray-600 mt-1">
                                            <span class="font-medium">Email:</span> {listing.seller().email()}
                                        </p>
                                    {/if}
                                    {#if listing.seller().phone()}
                                        <p class="text-sm text-gray-600 mt-1">
                                            <span class="font-medium">Phone:</span> {listing.seller().phone()}
                                        </p>
                                    {/if}
                                </div>
                            </div>
                        {/if}

                        <!-- Actions -->
                        <div class="mt-10 flex flex-col sm:flex-row sm:space-x-4">
                            {#if isAuthenticated}
                                {#if canEdit}
                                    <!-- Seller actions -->
                                    <button type="button"
                                            hx-post="/htmx/listings/{listing.id()}/status"
                                            hx-vals='{"status": "SOLD"}'
                                            hx-target="#alert-container"
                                            hx-swap="innerHTML"
                                            class="flex-1 bg-green-600 border border-transparent rounded-md py-3 px-8 flex items-center justify-center text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                        Mark as Sold
                                    </button>
                                {#else}
                                    <!-- Buyer actions -->
                                    {#if listing.availabilityStatus() == 'AVAILABLE'}
                                        <button type="button"
                                                hx-post="/htmx/listings/{listing.id()}/reserve"
                                                hx-target="#alert-container"
                                                hx-swap="innerHTML"
                                                class="flex-1 bg-blue-600 border border-transparent rounded-md py-3 px-8 flex items-center justify-center text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            Reserve for Purchase
                                        </button>
                                    {#else}
                                        <div class="flex-1 bg-gray-300 border border-transparent rounded-md py-3 px-8 flex items-center justify-center text-base font-medium text-gray-500 cursor-not-allowed">
                                            {listing.availabilityStatus().displayName()}
                                        </div>
                                    {/if}
                                {/if}
                            {#else}
                                <a href="/login" 
                                   class="flex-1 bg-blue-600 border border-transparent rounded-md py-3 px-8 flex items-center justify-center text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    Sign In to Purchase
                                </a>
                            {/if}
                        </div>

                        <!-- Description -->
                        <div class="mt-10">
                            <h3 class="text-sm font-medium text-gray-900">Description</h3>
                            <div class="mt-4 prose prose-sm text-gray-500">
                                <p>{listing.description()}</p>
                            </div>
                        </div>

                        <!-- Additional details -->
                        {#if listing.serviceHistory() && !listing.serviceHistory().isEmpty()}
                            <div class="mt-8">
                                <h3 class="text-sm font-medium text-gray-900">Service History</h3>
                                <div class="mt-4 prose prose-sm text-gray-500">
                                    <p>{listing.serviceHistory()}</p>
                                </div>
                            </div>
                        {/if}

                        {#if listing.warrantyInformation() && !listing.warrantyInformation().isEmpty()}
                            <div class="mt-8">
                                <h3 class="text-sm font-medium text-gray-900">Warranty Information</h3>
                                <div class="mt-4 prose prose-sm text-gray-500">
                                    <p>{listing.warrantyInformation()}</p>
                                </div>
                            </div>
                        {/if}

                        <!-- Policies -->
                        <div class="mt-8 border-t border-gray-200 pt-8">
                            <h3 class="text-sm font-medium text-gray-900">Purchase Policies</h3>
                            <div class="mt-4 space-y-2">
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">Inspection Window:</span> 24 hours after pickup
                                </p>
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">Escrow Policy:</span> Funds held until 24-hour inspection passes
                                </p>
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium">Refund Policy:</span> No returns after 24 hours
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {/content}
{/include}
