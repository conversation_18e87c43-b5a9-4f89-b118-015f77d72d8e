{#include layout.qute.html}
    {#title}Upload Photos - Bikeana{/title}
    {#content}
        <div class="min-h-screen bg-gray-50 py-8">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900">Upload Photos for Your Listing</h1>
                    <p class="mt-2 text-gray-600">Upload high-quality photos of your bike</p>
                </div>

                <!-- Alert Container -->
                <div id="alert-container" class="mb-6"></div>

                <!-- Photo Requirements -->
                <div class="mb-6 bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Photo Requirements</h3>
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div class="text-center">
                            <div class="w-full h-24 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center mb-2">
                                <span class="text-xs text-gray-500">Drive Side</span>
                            </div>
                            <p class="text-sm text-gray-600">Full bike view showing chain and gears</p>
                        </div>
                        <div class="text-center">
                            <div class="w-full h-24 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center mb-2">
                                <span class="text-xs text-gray-500">Non-Drive Side</span>
                            </div>
                            <p class="text-sm text-gray-600">Full bike view from opposite side</p>
                        </div>
                        <div class="text-center">
                            <div class="w-full h-24 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center mb-2">
                                <span class="text-xs text-gray-500">Drivetrain</span>
                            </div>
                            <p class="text-sm text-gray-600">Close-up of chain, cassette, derailleur</p>
                        </div>
                        <div class="text-center">
                            <div class="w-full h-24 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center mb-2">
                                <span class="text-xs text-gray-500">Brake</span>
                            </div>
                            <p class="text-sm text-gray-600">Clear view of brake system</p>
                        </div>
                        <div class="text-center">
                            <div class="w-full h-24 bg-gray-100 rounded border-2 border-gray-300 flex items-center justify-center mb-2">
                                <span class="text-xs text-gray-500">Frame Detail</span>
                            </div>
                            <p class="text-sm text-gray-600">Frame details, welds, features</p>
                        </div>
                    </div>
                </div>

                <!-- Upload Form -->
                <div class="bg-white rounded-lg shadow">
                    <form id="photo-upload-form" class="p-6">
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Select Photos (minimum 5 required)
                            </label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                            <span>Upload files</span>
                                            <input id="file-upload" name="files" type="file" class="sr-only" multiple accept="image/*" required>
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs text-gray-500">PNG, JPG, WebP up to 10MB each</p>
                                </div>
                            </div>
                        </div>

                        <!-- Photo Preview -->
                        <div id="photo-preview" class="mb-6" style="display: none;">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Selected Photos</h4>
                            <div id="preview-grid" class="grid grid-cols-2 md:grid-cols-5 gap-4"></div>
                        </div>

                        <!-- Photo Type Assignment -->
                        <div id="photo-types" class="mb-6" style="display: none;">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Assign Photo Types</h4>
                            <div id="type-assignment" class="space-y-3"></div>
                        </div>

                        <!-- Primary Photo Selection -->
                        <div id="primary-photo" class="mb-6" style="display: none;">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Select Primary Photo (will be shown in listings)
                            </label>
                            <select id="primary-photo-select" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </select>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex justify-between">
                            <button type="button" id="try-camera-btn" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                📸 Try Camera Instead
                            </button>
                            
                            <button type="submit" id="upload-btn" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50" disabled>
                                Upload Photos
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const fileInput = document.getElementById('file-upload');
                const photoPreview = document.getElementById('photo-preview');
                const previewGrid = document.getElementById('preview-grid');
                const photoTypes = document.getElementById('photo-types');
                const typeAssignment = document.getElementById('type-assignment');
                const primaryPhoto = document.getElementById('primary-photo');
                const primaryPhotoSelect = document.getElementById('primary-photo-select');
                const uploadBtn = document.getElementById('upload-btn');
                const tryCameraBtn = document.getElementById('try-camera-btn');
                
                let selectedFiles = [];
                
                const photoTypeOptions = [
                    { value: 'DRIVE_SIDE_FULL', label: 'Drive-side full-bike view' },
                    { value: 'NON_DRIVE_SIDE_FULL', label: 'Non-drive-side full-bike view' },
                    { value: 'DRIVETRAIN_CLOSEUP', label: 'Drivetrain close-up' },
                    { value: 'BRAKE_CLOSEUP', label: 'Brake close-up' },
                    { value: 'FRAME_DETAIL', label: 'Frame detail close-up' },
                    { value: 'GENERAL', label: 'General photo' }
                ];

                fileInput.addEventListener('change', handleFileSelection);
                tryCameraBtn.addEventListener('click', () => {
                    const listingId = new URLSearchParams(window.location.search).get('listingId');
                    window.location.href = `/listings/${listingId}/capture-photos`;
                });

                function handleFileSelection(event) {
                    selectedFiles = Array.from(event.target.files);
                    
                    if (selectedFiles.length < 5) {
                        alert('Please select at least 5 photos');
                        uploadBtn.disabled = true;
                        return;
                    }
                    
                    if (selectedFiles.length > 10) {
                        alert('Maximum 10 photos allowed');
                        selectedFiles = selectedFiles.slice(0, 10);
                    }
                    
                    displayPhotoPreview();
                    createTypeAssignment();
                    createPrimaryPhotoSelect();
                    uploadBtn.disabled = false;
                }

                function displayPhotoPreview() {
                    previewGrid.innerHTML = '';
                    photoPreview.style.display = 'block';
                    
                    selectedFiles.forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const div = document.createElement('div');
                            div.className = 'relative';
                            div.innerHTML = `
                                <img src="${e.target.result}" class="w-full h-24 object-cover rounded border">
                                <p class="text-xs text-gray-600 mt-1">Photo ${index + 1}</p>
                            `;
                            previewGrid.appendChild(div);
                        };
                        reader.readAsDataURL(file);
                    });
                }

                function createTypeAssignment() {
                    typeAssignment.innerHTML = '';
                    photoTypes.style.display = 'block';
                    
                    selectedFiles.forEach((file, index) => {
                        const div = document.createElement('div');
                        div.className = 'flex items-center space-x-3';
                        
                        const select = document.createElement('select');
                        select.className = 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm';
                        select.name = `photoType_${index}`;
                        
                        photoTypeOptions.forEach(option => {
                            const optionElement = document.createElement('option');
                            optionElement.value = option.value;
                            optionElement.textContent = option.label;
                            if (index < photoTypeOptions.length - 1) {
                                optionElement.selected = option.value === photoTypeOptions[index].value;
                            }
                            select.appendChild(optionElement);
                        });
                        
                        div.innerHTML = `
                            <label class="block text-sm font-medium text-gray-700 w-20">Photo ${index + 1}:</label>
                        `;
                        div.appendChild(select);
                        typeAssignment.appendChild(div);
                    });
                }

                function createPrimaryPhotoSelect() {
                    primaryPhotoSelect.innerHTML = '';
                    primaryPhoto.style.display = 'block';
                    
                    selectedFiles.forEach((file, index) => {
                        const option = document.createElement('option');
                        option.value = index;
                        option.textContent = `Photo ${index + 1}`;
                        if (index === 0) option.selected = true;
                        primaryPhotoSelect.appendChild(option);
                    });
                }

                document.getElementById('photo-upload-form').addEventListener('submit', async function(e) {
                    e.preventDefault();
                    
                    const listingId = new URLSearchParams(window.location.search).get('listingId');
                    if (!listingId) {
                        alert('Error: No listing ID found');
                        return;
                    }

                    const formData = new FormData();
                    const photoTypes = [];
                    
                    selectedFiles.forEach((file, index) => {
                        formData.append('files', file);
                        const typeSelect = document.querySelector(`select[name="photoType_${index}"]`);
                        photoTypes.push(typeSelect.value);
                    });
                    
                    photoTypes.forEach(type => formData.append('photoTypes', type));
                    formData.append('primaryPhotoIndex', primaryPhotoSelect.value);

                    try {
                        uploadBtn.disabled = true;
                        uploadBtn.textContent = 'Uploading...';
                        
                        const response = await fetch(`/api/listings/${listingId}/photos`, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
                            }
                        });

                        if (response.ok) {
                            window.location.href = `/listings/${listingId}`;
                        } else {
                            const error = await response.json();
                            alert(`Upload failed: ${error.message || 'Unknown error'}`);
                        }
                    } catch (error) {
                        console.error('Upload error:', error);
                        alert('Upload failed. Please try again.');
                    } finally {
                        uploadBtn.disabled = false;
                        uploadBtn.textContent = 'Upload Photos';
                    }
                });
            });
        </script>
    {/content}
{/include}
