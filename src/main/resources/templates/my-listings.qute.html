{#include layout.qute.html}
    {#title}My Listings - Bikeana{/title}
    {#content}
        <div class="min-h-screen bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <!-- Header -->
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">My Listings</h1>
                        <p class="mt-2 text-gray-600">Manage your bike listings</p>
                    </div>
                    <div class="mt-4 lg:mt-0">
                        <a href="/listings/create" 
                           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            Create New Listing
                        </a>
                    </div>
                </div>

                <!-- Alert Container -->
                <div id="alert-container" class="mb-6"></div>

                <!-- Listings -->
                {#if listings.size() > 0}
                    <div class="bg-white shadow overflow-hidden sm:rounded-md">
                        <ul class="divide-y divide-gray-200">
                            {#for listing in listings}
                                <li>
                                    <div class="px-4 py-4 flex items-center justify-between">
                                        <div class="flex items-center">
                                            <!-- Photo -->
                                            <div class="flex-shrink-0 h-20 w-20">
                                                {#if listing.primaryPhoto()}
                                                    <img class="h-20 w-20 rounded-lg object-cover"
                                                         src="/uploads/{listing.primaryPhoto().filePath()}"
                                                         alt="{listing.primaryPhoto().altText()}">
                                                {#else}
                                                    <div class="h-20 w-20 rounded-lg bg-gray-300 flex items-center justify-center">
                                                        <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                        </svg>
                                                    </div>
                                                {/if}
                                            </div>
                                            
                                            <!-- Details -->
                                            <div class="ml-4 flex-1">
                                                <div class="flex items-center justify-between">
                                                    <p class="text-lg font-medium text-gray-900">
                                                        <a href="/listings/{listing.seoSlug()}" class="hover:text-blue-600">
                                                            {listing.title()}
                                                        </a>
                                                    </p>
                                                </div>
                                                <p class="text-sm text-gray-600">
                                                    {listing.year()} {listing.brand()} {listing.model()}
                                                </p>
                                                <div class="mt-2 flex items-center space-x-4">
                                                    <span class="text-lg font-bold text-gray-900">${listing.askingPrice()}</span>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                        {listing.bikeType().displayName()}
                                                    </span>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        {listing.conditionTier().displayName()}
                                                    </span>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                        {#if listing.availabilityStatus() == 'AVAILABLE'}bg-green-100 text-green-800{/if}
                                                        {#if listing.availabilityStatus() == 'RESERVED'}bg-yellow-100 text-yellow-800{/if}
                                                        {#if listing.availabilityStatus() == 'SOLD'}bg-gray-100 text-gray-800{/if}">
                                                        {listing.availabilityStatus().displayName()}
                                                    </span>
                                                </div>
                                                <p class="text-sm text-gray-500 mt-1">
                                                    Listed on {listing.createdAt().toString()}
                                                </p>
                                            </div>
                                        </div>

                                        <!-- Actions -->
                                        <div class="flex items-center space-x-2">
                                            <a href="/listings/{listing.seoSlug()}"
                                               class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                View
                                            </a>

                                            {#if listing.availabilityStatus() == 'AVAILABLE'}
                                                <button type="button"
                                                        hx-post="/htmx/listings/{listing.id()}/status"
                                                        hx-vals='{"status": "SOLD"}'
                                                        hx-target="#alert-container"
                                                        hx-swap="innerHTML"
                                                        hx-confirm="Are you sure you want to mark this listing as sold?"
                                                        class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                                    Mark Sold
                                                </button>
                                            {/if}

                                            <div class="relative inline-block text-left">
                                                <button type="button"
                                                        onclick="toggleDropdown('dropdown-{listing.id()}')"
                                                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                                    </svg>
                                                </button>

                                                <div id="dropdown-{listing.id()}"
                                                     class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                                                    <div class="py-1">
                                                        <button type="button"
                                                                hx-post="/htmx/listings/{listing.id()}/status"
                                                                hx-vals='{"status": "INACTIVE"}'
                                                                hx-target="#alert-container"
                                                                hx-swap="innerHTML"
                                                                hx-confirm="Are you sure you want to deactivate this listing?"
                                                                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                            Deactivate
                                                        </button>
                                                        {#if listing.availabilityStatus() == 'SOLD'}
                                                            <button type="button"
                                                                    hx-post="/htmx/listings/{listing.id()}/status"
                                                                    hx-vals='{"status": "ACTIVE"}'
                                                                    hx-target="#alert-container"
                                                                    hx-swap="innerHTML"
                                                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                                Reactivate
                                                            </button>
                                                        {/if}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            {/for}
                        </ul>
                    </div>
                {#else}
                    <!-- Empty state -->
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No listings yet</h3>
                        <p class="mt-1 text-sm text-gray-500">Get started by creating your first bike listing.</p>
                        <div class="mt-6">
                            <a href="/listings/create" 
                               class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Create Your First Listing
                            </a>
                        </div>
                    </div>
                {/if}
            </div>
        </div>

        <script>
            function toggleDropdown(id) {
                const dropdown = document.getElementById(id);
                dropdown.classList.toggle('hidden');
                
                // Close other dropdowns
                document.querySelectorAll('[id^="dropdown-"]').forEach(el => {
                    if (el.id !== id) {
                        el.classList.add('hidden');
                    }
                });
            }

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('[onclick*="toggleDropdown"]')) {
                    document.querySelectorAll('[id^="dropdown-"]').forEach(el => {
                        el.classList.add('hidden');
                    });
                }
            });
        </script>
    {/content}
{/include}
