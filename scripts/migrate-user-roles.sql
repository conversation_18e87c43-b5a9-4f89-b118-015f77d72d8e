-- Migration script to simplify user roles
-- Converts <PERSON><PERSON>Y<PERSON> and PR<PERSON><PERSON><PERSON>_SELLER roles to USER role
-- Keeps SHOP role unchanged

-- Update existing BUYER and PRIVATE_SELLER users to USER role
UPDATE users 
SET role = 'USER' 
WHERE role IN ('BUYER', 'PRIVATE_SELLER');

-- Verify the migration
SELECT 
    role,
    COUNT(*) as user_count
FROM users 
GROUP BY role
ORDER BY role;

-- Expected result: Only 'SHOP' and 'USER' roles should remain
