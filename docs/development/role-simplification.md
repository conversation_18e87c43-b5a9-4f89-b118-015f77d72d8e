# User Role Simplification Implementation

## Overview

This document describes the implementation of user role simplification in Bikeana, where the original three-role system (BUYER, PRIVATE_SELLER, SHOP) has been simplified to a two-role system (USER, SHOP).

## Business Rationale

### Benefits of Role Simplification

1. **Reduced User Friction**: Users no longer need to decide their primary purpose at signup
2. **Natural User Journey**: Reflects reality that many buyers become sellers and vice versa
3. **Simplified Permissions**: Authorization logic becomes clearer with fewer conditional paths
4. **Reduced Development Overhead**: Less code to maintain with fewer user types
5. **Improved Conversion**: No need for users to "upgrade" accounts to sell items

### Role Mapping

| Original Role | New Role | Description |
|---------------|----------|-------------|
| BUYER | USER | Individual users who can buy and sell bikes |
| PRIVATE_SELLER | USER | Individual users who can buy and sell bikes |
| SHOP | SHOP | Business accounts for bike shops and dealers |

## Implementation Details

### Database Changes

- **User Entity**: Updated role field comment to reflect new roles
- **Migration Script**: Created `scripts/migrate-user-roles.sql` to convert existing data
- **No Schema Changes**: Using existing String field, no database structure changes needed

### API Changes

#### SignupRequestDto
- Updated validation regex from `^(SHOP|PRIVATE_SELLER|BUYER)$` to `^(SHOP|USER)$`
- Updated validation message and JavaDoc comments

#### UserResponseDto
- Updated JavaDoc comment for role field

### Business Logic Updates

#### BikeListingService
- Updated `isValidSellerRole()` method to accept "USER" instead of "PRIVATE_SELLER"
- Both USER and SHOP roles can now create listings

#### WebController
- Updated listing creation permission check to use "USER" instead of "PRIVATE_SELLER"

### Frontend Changes

#### Signup Form
- Simplified role selection to two options:
  - "Individual - I want to buy and sell bikes" (USER)
  - "Business - I'm a bike shop or dealer" (SHOP)

### Test Updates

#### BikeListingServiceTest
- Updated test data creation to use new roles
- Added test for SHOP role listing creation
- Updated invalid role test to use truly invalid role

## Migration Strategy

### For Development Environment
Since the development environment uses `ddl-auto: create-drop`, the schema is recreated on each startup. The new role validation will be applied automatically.

### For Production Environment
1. Run the migration script `scripts/migrate-user-roles.sql`
2. Deploy the updated application code
3. Verify role conversion was successful

### Migration Script
```sql
-- Update existing BUYER and PRIVATE_SELLER users to USER role
UPDATE users 
SET role = 'USER' 
WHERE role IN ('BUYER', 'PRIVATE_SELLER');
```

## User Experience Impact

### Registration Flow
- **Before**: Users had to choose between BUYER, PRIVATE_SELLER, or SHOP
- **After**: Users choose between Individual (USER) or Business (SHOP)

### Functionality
- **USER Role**: Can both buy and sell bikes, access all marketplace features
- **SHOP Role**: Same as before, designed for business accounts

### Permissions
- **Listing Creation**: Both USER and SHOP can create listings
- **Listing Management**: Users can manage their own listings regardless of role
- **Profile Management**: All users can update their profiles

## Testing Strategy

### Unit Tests
- Updated existing tests to use new role structure
- Added tests for both USER and SHOP role functionality
- Verified invalid role handling

### Integration Tests
- Tested signup flow with new role options
- Verified listing creation works for both roles
- Tested role-based authorization

## Backward Compatibility

### Data Migration
- Existing BUYER users → USER role
- Existing PRIVATE_SELLER users → USER role
- Existing SHOP users → unchanged

### API Compatibility
- Old role values in requests will be rejected with validation errors
- Clients must update to use new role values

## Future Considerations

### Potential Enhancements
1. **Role-based Features**: Could add shop-specific features (business hours, multiple locations)
2. **User Preferences**: Could add user preferences for buying vs selling focus
3. **Analytics**: Track user behavior patterns between former buyers/sellers

### Monitoring
- Monitor signup conversion rates after role simplification
- Track user engagement with both buying and selling features
- Analyze support requests related to role confusion

## Files Modified

### Core Application Files
- `src/main/java/com/bikeana/user/domain/User.java`
- `src/main/java/com/bikeana/user/api/SignupRequestDto.java`
- `src/main/java/com/bikeana/user/api/UserResponseDto.java`
- `src/main/java/com/bikeana/listing/domain/BikeListingService.java`
- `src/main/java/com/bikeana/web/WebController.java`

### Frontend Templates
- `src/main/resources/templates/signup.qute.html`

### Documentation
- `docs/development/stories.md`
- `docs/development/role-simplification.md` (this file)

### Test Files
- `src/test/java/com/bikeana/listing/domain/BikeListingServiceTest.java`

### Migration Scripts
- `scripts/migrate-user-roles.sql`

## Validation

### Pre-deployment Checklist
- [ ] All tests pass with new role structure
- [ ] Signup form displays correct role options
- [ ] Listing creation works for both USER and SHOP roles
- [ ] Migration script tested on copy of production data
- [ ] Documentation updated

### Post-deployment Verification
- [ ] Existing users can still log in
- [ ] New users can register with simplified roles
- [ ] All role-based functionality works correctly
- [ ] No authorization errors in logs
